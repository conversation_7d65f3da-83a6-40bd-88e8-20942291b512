/**
 * Test script to verify gRPC method registration for AST Database
 * This script checks if the method names match between client and server
 */

const fs = require('fs');
const path = require('path');

function testGrpcMethodRegistration() {
    console.log("🧪 Testing gRPC Method Registration for AST Database...");
    
    try {
        // Test 1: Check protobuf service definition
        console.log("\n📋 Checking protobuf service definition...");
        
        const astdbProtoPath = path.join(__dirname, 'src/shared/proto/astdb.ts');
        if (!fs.existsSync(astdbProtoPath)) {
            throw new Error("astdb.ts protobuf file not found");
        }
        
        const astdbContent = fs.readFileSync(astdbProtoPath, 'utf8');
        
        // Extract method definitions
        const methodRegex = /(\w+):\s*{\s*name:\s*"(\w+)"/g;
        const methods = [];
        let match;
        
        while ((match = methodRegex.exec(astdbContent)) !== null) {
            methods.push({
                key: match[1],      // e.g., "startWorkspaceScan"
                name: match[2]      // e.g., "StartWorkspaceScan"
            });
        }
        
        console.log("Found protobuf methods:");
        methods.forEach(method => {
            console.log(`  - Key: ${method.key}, Name: ${method.name}`);
        });
        
        // Test 2: Check controller registration
        console.log("\n🎯 Checking controller method registration...");
        
        const methodsPath = path.join(__dirname, 'src/core/controller/astDatabase/methods.ts');
        if (!fs.existsSync(methodsPath)) {
            throw new Error("astDatabase methods.ts file not found");
        }
        
        const methodsContent = fs.readFileSync(methodsPath, 'utf8');
        
        // Extract registered method names
        const registrationRegex = /registerMethod\("(\w+)"/g;
        const registeredMethods = [];
        
        while ((match = registrationRegex.exec(methodsContent)) !== null) {
            registeredMethods.push(match[1]);
        }
        
        console.log("Registered controller methods:");
        registeredMethods.forEach(method => {
            console.log(`  - ${method}`);
        });
        
        // Test 3: Verify method name matching
        console.log("\n🔍 Verifying method name matching...");
        
        let allMatched = true;
        const expectedMethods = methods.map(m => m.name); // Use the "name" field (uppercase)
        
        for (const expectedMethod of expectedMethods) {
            if (registeredMethods.includes(expectedMethod)) {
                console.log(`  ✅ ${expectedMethod} - MATCHED`);
            } else {
                console.log(`  ❌ ${expectedMethod} - NOT FOUND in registration`);
                allMatched = false;
            }
        }
        
        // Check for extra registered methods
        for (const registeredMethod of registeredMethods) {
            if (!expectedMethods.includes(registeredMethod)) {
                console.log(`  ⚠️ ${registeredMethod} - EXTRA (not in protobuf)`);
            }
        }
        
        // Test 4: Check service configuration
        console.log("\n⚙️ Checking service configuration...");
        
        const configPath = path.join(__dirname, 'src/core/controller/grpc-service-config.ts');
        if (!fs.existsSync(configPath)) {
            throw new Error("grpc-service-config.ts file not found");
        }
        
        const configContent = fs.readFileSync(configPath, 'utf8');
        
        if (configContent.includes('"cline.AstDatabaseService"')) {
            console.log("  ✅ AstDatabaseService is registered in service config");
        } else {
            console.log("  ❌ AstDatabaseService is NOT registered in service config");
            allMatched = false;
        }
        
        if (configContent.includes('handleAstDatabaseServiceRequest')) {
            console.log("  ✅ AstDatabase request handler is configured");
        } else {
            console.log("  ❌ AstDatabase request handler is NOT configured");
            allMatched = false;
        }
        
        // Test 5: Check webview client usage
        console.log("\n🌐 Checking webview client usage...");
        
        const webviewPath = path.join(__dirname, 'webview-ui/src/components/settings/CodebaseSettingsSection.tsx');
        if (fs.existsSync(webviewPath)) {
            const webviewContent = fs.readFileSync(webviewPath, 'utf8');
            
            if (webviewContent.includes('AstDatabaseServiceClient.startWorkspaceScan')) {
                console.log("  ✅ Webview uses AstDatabaseServiceClient.startWorkspaceScan");
            } else {
                console.log("  ❌ Webview does NOT use AstDatabaseServiceClient.startWorkspaceScan");
            }
            
            if (webviewContent.includes('StartScanRequest.fromPartial')) {
                console.log("  ✅ Webview uses correct protobuf API (fromPartial)");
            } else {
                console.log("  ⚠️ Webview may not use correct protobuf API");
            }
        } else {
            console.log("  ⚠️ CodebaseSettingsSection.tsx not found");
        }
        
        // Summary
        console.log("\n📊 Test Summary:");
        if (allMatched) {
            console.log("✅ All gRPC method registrations are correct!");
            console.log("✅ The 'Unknown astDatabase method: StartWorkspaceScan' error should be fixed.");
        } else {
            console.log("❌ Some method registrations are incorrect.");
            console.log("❌ The gRPC error may still occur.");
        }
        
        console.log("\n🚀 Next Steps:");
        console.log("1. Restart VSCode extension (F5 in debug mode)");
        console.log("2. Open Cline settings and go to Codebase tab");
        console.log("3. Click 'Scan Codebase' button");
        console.log("4. Check if the error is resolved");
        
        return allMatched;
        
    } catch (error) {
        console.error("❌ Test failed:", error.message);
        return false;
    }
}

// Run the test
if (require.main === module) {
    const success = testGrpcMethodRegistration();
    process.exit(success ? 0 : 1);
}

module.exports = { testGrpcMethodRegistration };
